<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Video.js YouTube Example</title>

  <!-- Video.js CSS -->
  <link href="https://vjs.zencdn.net/8.10.0/video-js.css" rel="stylesheet" />

  <style>
    body {
      background: #111;
      padding: 40px;
      color: #fff;
      font-family: sans-serif;
    }
    .video-container {
      max-width: 800px;
      margin: auto;
    }
  </style>
</head>
<body>
  <div class="video-container">
    <video
      id="my-video"
      class="video-js vjs-default-skin"
      controls
      width="800"
      height="450"
      data-setup='{
        "techOrder": ["youtube"],
        "sources": [{
          "type": "video/youtube",
          "src": "https://www.youtube.com/watch?v=-97NZ5z7mYA"
        }],
        "youtube": {
          "modestbranding": 1,
          "rel": 0,
          "showinfo": 0
        },
        "poster": "https://img.youtube.com/vi/-97NZ5z7mYA/maxresdefault.jpg",
        "autoplay": true
      }'
    ></video>
  </div>

  <!-- Video.js JS -->
  <script src="https://vjs.zencdn.net/8.10.0/video.min.js"></script>
  <!-- Video.js YouTube plugin -->
  <script src="https://cdn.jsdelivr.net/npm/videojs-youtube@3.0.1/dist/Youtube.min.js"></script>
</body>
</html>
